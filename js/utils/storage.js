/**
 * Split-Second Spark - 存储服务
 * 统一的键值存储接口，支持多种后端存储方案
 */

class StorageService {
    constructor() {
        this.isInitialized = false;
        this.storageType = 'memory'; // 'indexeddb', 'localstorage', 'memory'
        this.memoryStorage = new Map();
        this.dbName = 'SplitSecondSparkDB';
        this.dbVersion = 1;
        this.db = null;
    }

    /**
     * 初始化存储服务
     * 自动选择最佳的存储方案
     * 优先检查是否存在 edgeOneStorage 云存储方案
     */
    async init() {
        try {
            console.log('🗄️ 初始化存储服务...');

            // 首先检查是否存在 edgeOneStorage 全局变量
            if (typeof window !== 'undefined' && window.edgeOneStorage) {
                console.log('🌐 检测到 edgeOneStorage，使用云存储方案');
                await this.initEdgeOneStorage();
                this.storageType = 'edgeone';
                console.log('✅ 使用 EdgeOne 云存储');
                this.isInitialized = true;
                console.log(`✅ 存储服务初始化完成，使用: ${this.storageType}`);
                return;
            }

            // 如果没有云存储，使用原有逻辑
            console.log('📱 使用本地存储方案');

            // 尝试使用 IndexedDB
            if (this.isIndexedDBSupported()) {
                try {
                    await this.initIndexedDB();
                    this.storageType = 'indexeddb';
                    console.log('✅ 使用 IndexedDB 存储');
                } catch (error) {
                    console.warn('⚠️ IndexedDB 初始化失败，尝试 localStorage:', error);
                    this.initLocalStorage();
                }
            } else if (this.isLocalStorageSupported()) {
                this.initLocalStorage();
            } else {
                console.warn('⚠️ 浏览器不支持持久化存储，使用内存存储');
                this.storageType = 'memory';
            }

            this.isInitialized = true;
            console.log(`✅ 存储服务初始化完成，使用: ${this.storageType}`);

        } catch (error) {
            console.error('❌ 存储服务初始化失败:', error);
            this.storageType = 'memory';
            this.isInitialized = true;
        }
    }

    /**
     * 检查是否支持 IndexedDB
     */
    isIndexedDBSupported() {
        return 'indexedDB' in window && window.indexedDB !== null;
    }

    /**
     * 检查是否支持 localStorage
     */
    isLocalStorageSupported() {
        try {
            const test = '__storage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 初始化 IndexedDB
     */
    async initIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => {
                reject(new Error('IndexedDB 打开失败'));
            };
            
            request.onsuccess = (event) => {
                this.db = event.target.result;
                resolve();
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // 创建对象存储
                if (!db.objectStoreNames.contains('keyvalue')) {
                    const store = db.createObjectStore('keyvalue', { keyPath: 'key' });
                    store.createIndex('key', 'key', { unique: true });
                }
            };
        });
    }

    /**
     * 初始化 localStorage
     */
    initLocalStorage() {
        this.storageType = 'localstorage';
        console.log('✅ 使用 localStorage 存储');
    }

    /**
     * 初始化 EdgeOne 云存储
     */
    async initEdgeOneStorage() {
        try {
            // 检查 edgeOneStorage 是否可用
            if (!window.edgeOneStorage) {
                throw new Error('EdgeOne 存储服务不可用');
            }

            // 如果 edgeOneStorage 有初始化方法，调用它
            if (typeof window.edgeOneStorage.init === 'function') {
                await window.edgeOneStorage.init();
                console.log('🌐 EdgeOne 存储服务初始化完成');
            }

            // 测试连接
            if (typeof window.edgeOneStorage.test === 'function') {
                await window.edgeOneStorage.test();
                console.log('🌐 EdgeOne 存储连接测试成功');
            }

        } catch (error) {
            console.error('❌ EdgeOne 存储初始化失败:', error);
            throw error;
        }
    }

    /**
     * 保存数据
     * @param {string} key - 键名
     * @param {any} value - 值
     */
    async put(key, value) {
        if (!this.isInitialized) {
            await this.init();
        }

        try {
            const serializedValue = JSON.stringify(value);

            switch (this.storageType) {
                case 'edgeone':
                    return await this.putEdgeOne(key, serializedValue);
                case 'indexeddb':
                    return await this.putIndexedDB(key, serializedValue);
                case 'localstorage':
                    return this.putLocalStorage(key, serializedValue);
                case 'memory':
                default:
                    return this.putMemory(key, serializedValue);
            }
        } catch (error) {
            console.error(`❌ 保存数据失败 [${key}]:`, error);
            throw error;
        }
    }

    /**
     * 读取数据
     * @param {string} key - 键名
     * @returns {any} 值
     */
    async get(key) {
        if (!this.isInitialized) {
            await this.init();
        }

        try {
            let serializedValue;

            switch (this.storageType) {
                case 'edgeone':
                    serializedValue = await this.getEdgeOne(key);
                    break;
                case 'indexeddb':
                    serializedValue = await this.getIndexedDB(key);
                    break;
                case 'localstorage':
                    serializedValue = this.getLocalStorage(key);
                    break;
                case 'memory':
                default:
                    serializedValue = this.getMemory(key);
                    break;
            }

            return serializedValue ? JSON.parse(serializedValue) : null;
        } catch (error) {
            console.error(`❌ 读取数据失败 [${key}]:`, error);
            return null;
        }
    }

    /**
     * 删除数据
     * @param {string} key - 键名
     */
    async delete(key) {
        if (!this.isInitialized) {
            await this.init();
        }

        try {
            switch (this.storageType) {
                case 'edgeone':
                    return await this.deleteEdgeOne(key);
                case 'indexeddb':
                    return await this.deleteIndexedDB(key);
                case 'localstorage':
                    return this.deleteLocalStorage(key);
                case 'memory':
                default:
                    return this.deleteMemory(key);
            }
        } catch (error) {
            console.error(`❌ 删除数据失败 [${key}]:`, error);
            throw error;
        }
    }

    /**
     * 列出指定前缀的所有键
     * @param {string} prefix - 前缀
     * @returns {Array<string>} 键名数组
     */
    async list(prefix = '') {
        if (!this.isInitialized) {
            await this.init();
        }

        try {
            switch (this.storageType) {
                case 'edgeone':
                    return await this.listEdgeOne(prefix);
                case 'indexeddb':
                    return await this.listIndexedDB(prefix);
                case 'localstorage':
                    return this.listLocalStorage(prefix);
                case 'memory':
                default:
                    return this.listMemory(prefix);
            }
        } catch (error) {
            console.error(`❌ 列出键失败 [${prefix}]:`, error);
            return [];
        }
    }

    // IndexedDB 操作方法
    async putIndexedDB(key, value) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['keyvalue'], 'readwrite');
            const store = transaction.objectStore('keyvalue');
            const request = store.put({ key, value, timestamp: Date.now() });
            
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    async getIndexedDB(key) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['keyvalue'], 'readonly');
            const store = transaction.objectStore('keyvalue');
            const request = store.get(key);
            
            request.onsuccess = () => {
                const result = request.result;
                resolve(result ? result.value : null);
            };
            request.onerror = () => reject(request.error);
        });
    }

    async deleteIndexedDB(key) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['keyvalue'], 'readwrite');
            const store = transaction.objectStore('keyvalue');
            const request = store.delete(key);
            
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    async listIndexedDB(prefix) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['keyvalue'], 'readonly');
            const store = transaction.objectStore('keyvalue');
            const request = store.getAllKeys();
            
            request.onsuccess = () => {
                const keys = request.result.filter(key => key.startsWith(prefix));
                resolve(keys);
            };
            request.onerror = () => reject(request.error);
        });
    }

    // localStorage 操作方法
    putLocalStorage(key, value) {
        localStorage.setItem(`sss_${key}`, value);
    }

    getLocalStorage(key) {
        return localStorage.getItem(`sss_${key}`);
    }

    deleteLocalStorage(key) {
        localStorage.removeItem(`sss_${key}`);
    }

    listLocalStorage(prefix) {
        const keys = [];
        const fullPrefix = `sss_${prefix}`;
        
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(fullPrefix)) {
                keys.push(key.substring(4)); // 移除 'sss_' 前缀
            }
        }
        
        return keys;
    }

    // 内存存储操作方法
    putMemory(key, value) {
        this.memoryStorage.set(key, value);
    }

    getMemory(key) {
        return this.memoryStorage.get(key) || null;
    }

    deleteMemory(key) {
        this.memoryStorage.delete(key);
    }

    listMemory(prefix) {
        const keys = [];
        for (const key of this.memoryStorage.keys()) {
            if (key.startsWith(prefix)) {
                keys.push(key);
            }
        }
        return keys;
    }

    // EdgeOne 云存储操作方法
    /**
     * EdgeOne 云存储 - 保存数据
     * @param {string} key - 键名
     * @param {string} value - 序列化后的值
     */
    async putEdgeOne(key, value) {
        try {
            if (!window.edgeOneStorage) {
                throw new Error('EdgeOne 存储服务不可用');
            }

            // 调用 EdgeOne 存储的 put 方法
            if (typeof window.edgeOneStorage.put === 'function') {
                await window.edgeOneStorage.put(key, value);
                console.log(`🌐 EdgeOne 存储保存成功: ${key}`);
            } else if (typeof window.edgeOneStorage.setItem === 'function') {
                // 兼容 localStorage 风格的 API
                await window.edgeOneStorage.setItem(key, value);
                console.log(`🌐 EdgeOne 存储保存成功: ${key}`);
            } else {
                throw new Error('EdgeOne 存储服务不支持 put 或 setItem 方法');
            }
        } catch (error) {
            console.error(`❌ EdgeOne 存储保存失败 [${key}]:`, error);
            throw error;
        }
    }

    // EdgeOne 云存储操作方法
    /**
     * EdgeOne 云存储 - 保存数据
     * @param {string} key - 键名
     * @param {string} value - 序列化后的值
     */
    async putEdgeOne(key, value) {
        try {
            if (!window.edgeOneStorage) {
                throw new Error('EdgeOne 存储服务不可用');
            }

            // 调用 EdgeOne 存储的 put 方法
            if (typeof window.edgeOneStorage.put === 'function') {
                await window.edgeOneStorage.put(key, value);
                console.log(`🌐 EdgeOne 存储保存成功: ${key}`);
            } else if (typeof window.edgeOneStorage.setItem === 'function') {
                // 兼容 localStorage 风格的 API
                await window.edgeOneStorage.setItem(key, value);
                console.log(`🌐 EdgeOne 存储保存成功: ${key}`);
            } else {
                throw new Error('EdgeOne 存储服务不支持 put 或 setItem 方法');
            }
        } catch (error) {
            console.error(`❌ EdgeOne 存储保存失败 [${key}]:`, error);
            throw error;
        }
    }

    /**
     * EdgeOne 云存储 - 读取数据
     * @param {string} key - 键名
     * @returns {string|null} 序列化后的值
     */
    async getEdgeOne(key) {
        try {
            if (!window.edgeOneStorage) {
                throw new Error('EdgeOne 存储服务不可用');
            }

            let result = null;

            // 调用 EdgeOne 存储的 get 方法
            if (typeof window.edgeOneStorage.get === 'function') {
                result = await window.edgeOneStorage.get(key);
            } else if (typeof window.edgeOneStorage.getItem === 'function') {
                // 兼容 localStorage 风格的 API
                result = await window.edgeOneStorage.getItem(key);
            } else {
                throw new Error('EdgeOne 存储服务不支持 get 或 getItem 方法');
            }

            console.log(`🌐 EdgeOne 存储读取${result ? '成功' : '为空'}: ${key}`);
            return result;
        } catch (error) {
            console.error(`❌ EdgeOne 存储读取失败 [${key}]:`, error);
            return null;
        }
    }

    /**
     * EdgeOne 云存储 - 删除数据
     * @param {string} key - 键名
     */
    async deleteEdgeOne(key) {
        try {
            if (!window.edgeOneStorage) {
                throw new Error('EdgeOne 存储服务不可用');
            }

            // 调用 EdgeOne 存储的 delete 方法
            if (typeof window.edgeOneStorage.delete === 'function') {
                await window.edgeOneStorage.delete(key);
                console.log(`🌐 EdgeOne 存储删除成功: ${key}`);
            } else if (typeof window.edgeOneStorage.removeItem === 'function') {
                // 兼容 localStorage 风格的 API
                await window.edgeOneStorage.removeItem(key);
                console.log(`🌐 EdgeOne 存储删除成功: ${key}`);
            } else {
                throw new Error('EdgeOne 存储服务不支持 delete 或 removeItem 方法');
            }
        } catch (error) {
            console.error(`❌ EdgeOne 存储删除失败 [${key}]:`, error);
            throw error;
        }
    }

    /**
     * EdgeOne 云存储 - 列出键
     * @param {string} prefix - 前缀
     * @returns {Array<string>} 键名数组
     */
    async listEdgeOne(prefix = '') {
        try {
            if (!window.edgeOneStorage) {
                throw new Error('EdgeOne 存储服务不可用');
            }

            let keys = [];

            // 调用 EdgeOne 存储的 list 方法
            if (typeof window.edgeOneStorage.list === 'function') {
                keys = await window.edgeOneStorage.list(prefix);
            } else if (typeof window.edgeOneStorage.keys === 'function') {
                // 获取所有键然后过滤
                const allKeys = await window.edgeOneStorage.keys();
                keys = allKeys.filter(key => key.startsWith(prefix));
            } else {
                console.warn('⚠️ EdgeOne 存储服务不支持 list 或 keys 方法，返回空数组');
                keys = [];
            }

            console.log(`🌐 EdgeOne 存储列出键成功，前缀: ${prefix}，数量: ${keys.length}`);
            return keys;
        } catch (error) {
            console.error(`❌ EdgeOne 存储列出键失败 [${prefix}]:`, error);
            return [];
        }
    }

    /**
     * EdgeOne 云存储 - 读取数据
     * @param {string} key - 键名
     * @returns {string|null} 序列化后的值
     */
    async getEdgeOne(key) {
        try {
            if (!window.edgeOneStorage) {
                throw new Error('EdgeOne 存储服务不可用');
            }

            let result = null;

            // 调用 EdgeOne 存储的 get 方法
            if (typeof window.edgeOneStorage.get === 'function') {
                result = await window.edgeOneStorage.get(key);
            } else if (typeof window.edgeOneStorage.getItem === 'function') {
                // 兼容 localStorage 风格的 API
                result = await window.edgeOneStorage.getItem(key);
            } else {
                throw new Error('EdgeOne 存储服务不支持 get 或 getItem 方法');
            }

            console.log(`🌐 EdgeOne 存储读取${result ? '成功' : '为空'}: ${key}`);
            return result;
        } catch (error) {
            console.error(`❌ EdgeOne 存储读取失败 [${key}]:`, error);
            return null;
        }
    }

    /**
     * EdgeOne 云存储 - 删除数据
     * @param {string} key - 键名
     */
    async deleteEdgeOne(key) {
        try {
            if (!window.edgeOneStorage) {
                throw new Error('EdgeOne 存储服务不可用');
            }

            // 调用 EdgeOne 存储的 delete 方法
            if (typeof window.edgeOneStorage.delete === 'function') {
                await window.edgeOneStorage.delete(key);
                console.log(`🌐 EdgeOne 存储删除成功: ${key}`);
            } else if (typeof window.edgeOneStorage.removeItem === 'function') {
                // 兼容 localStorage 风格的 API
                await window.edgeOneStorage.removeItem(key);
                console.log(`🌐 EdgeOne 存储删除成功: ${key}`);
            } else {
                throw new Error('EdgeOne 存储服务不支持 delete 或 removeItem 方法');
            }
        } catch (error) {
            console.error(`❌ EdgeOne 存储删除失败 [${key}]:`, error);
            throw error;
        }
    }

    /**
     * EdgeOne 云存储 - 列出键
     * @param {string} prefix - 前缀
     * @returns {Array<string>} 键名数组
     */
    async listEdgeOne(prefix = '') {
        try {
            if (!window.edgeOneStorage) {
                throw new Error('EdgeOne 存储服务不可用');
            }

            let keys = [];

            // 调用 EdgeOne 存储的 list 方法
            if (typeof window.edgeOneStorage.list === 'function') {
                keys = await window.edgeOneStorage.list(prefix);
            } else if (typeof window.edgeOneStorage.keys === 'function') {
                // 获取所有键然后过滤
                const allKeys = await window.edgeOneStorage.keys();
                keys = allKeys.filter(key => key.startsWith(prefix));
            } else {
                console.warn('⚠️ EdgeOne 存储服务不支持 list 或 keys 方法，返回空数组');
                keys = [];
            }

            console.log(`🌐 EdgeOne 存储列出键成功，前缀: ${prefix}，数量: ${keys.length}`);
            return keys;
        } catch (error) {
            console.error(`❌ EdgeOne 存储列出键失败 [${prefix}]:`, error);
            return [];
        }
    }

    /**
     * 清空所有数据
     */
    async clear() {
        try {
            switch (this.storageType) {
                case 'edgeone':
                    // EdgeOne 云存储清空
                    if (window.edgeOneStorage && typeof window.edgeOneStorage.clear === 'function') {
                        await window.edgeOneStorage.clear();
                        console.log('🌐 EdgeOne 存储数据已清空');
                    } else {
                        console.warn('⚠️ EdgeOne 存储服务不支持 clear 方法');
                    }
                    break;
                case 'indexeddb':
                    const transaction = this.db.transaction(['keyvalue'], 'readwrite');
                    const store = transaction.objectStore('keyvalue');
                    await new Promise((resolve, reject) => {
                        const request = store.clear();
                        request.onsuccess = () => resolve();
                        request.onerror = () => reject(request.error);
                    });
                    break;
                case 'localstorage':
                    const keys = this.listLocalStorage('');
                    keys.forEach(key => this.deleteLocalStorage(key));
                    break;
                case 'memory':
                default:
                    this.memoryStorage.clear();
                    break;
            }
            console.log('✅ 存储数据已清空');
        } catch (error) {
            console.error('❌ 清空存储数据失败:', error);
            throw error;
        }
    }

    /**
     * 获取存储信息
     */
    getStorageInfo() {
        const info = {
            type: this.storageType,
            isInitialized: this.isInitialized,
            memorySize: this.memoryStorage.size
        };

        // 如果是 EdgeOne 存储，添加额外信息
        if (this.storageType === 'edgeone' && window.edgeOneStorage) {
            info.cloudStorage = {
                available: true,
                hasGetInfo: typeof window.edgeOneStorage.getInfo === 'function'
            };

            // 如果 EdgeOne 存储有 getInfo 方法，获取详细信息
            if (typeof window.edgeOneStorage.getInfo === 'function') {
                try {
                    info.cloudStorage.details = window.edgeOneStorage.getInfo();
                } catch (error) {
                    console.warn('⚠️ 获取 EdgeOne 存储详细信息失败:', error);
                }
            }
        }

        return info;
    }
}

// 创建全局存储服务实例
const storageService = new StorageService();

// 导出服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { StorageService, storageService };
} else {
    window.storageService = storageService;
}
