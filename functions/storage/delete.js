/**
 * Cloudflare Functions - 删除指定键的数据 API 端点
 * 
 * 功能说明：
 * - 接收 HTTP 请求，从请求中提取键(key)参数
 * - 调用 Cloudflare KV 存储服务删除存储的数据
 * - 返回 JSON 格式的响应，包含删除操作结果
 * - 支持 CORS 跨域访问
 * 
 * 请求方式：DELETE
 * 请求参数：
 * - key: 要删除的键名（字符串）
 * 
 * 响应格式：
 * {
 *   "success": true/false,
 *   "message": "操作结果描述",
 *   "key": "删除的键名",
 *   "deleted": true/false,
 *   "timestamp": "操作时间戳"
 * }
 */

export async function onRequest({ request, params, env }) {
  try {
    // 设置 CORS 响应头
    const corsHeaders = {
      'Content-Type': 'application/json; charset=UTF-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // 处理 OPTIONS 预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: corsHeaders,
      });
    }

    // 只允许 DELETE 请求
    if (request.method !== 'DELETE') {
      return new Response(JSON.stringify({
        success: false,
        message: '仅支持 DELETE 请求方法',
        error: 'METHOD_NOT_ALLOWED'
      }), {
        status: 405,
        headers: corsHeaders,
      });
    }

    let key;

    // 从请求中提取参数
    const contentType = request.headers.get('content-type') || '';
    
    if (contentType.includes('application/json')) {
      // 处理 JSON 格式的请求体
      const requestData = await request.json();
      key = requestData.key;
    } else {
      // 从 URL 查询参数中获取
      const url = new URL(request.url);
      key = url.searchParams.get('key');
    }

    // 验证必需参数
    if (!key) {
      return new Response(JSON.stringify({
        success: false,
        message: '缺少必需参数：key（键名）',
        error: 'MISSING_KEY_PARAMETER'
      }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // 获取 KV 存储命名空间
    const kvNamespace = edgeOneStorage;

    if (!kvNamespace) {
      throw new Error('KV 存储命名空间未配置，请检查环境变量设置');
    }

    // 首先检查键是否存在
    const existingValue = await kvNamespace.get(key);
    const keyExists = existingValue !== null && existingValue !== undefined;

    if (!keyExists) {
      return new Response(JSON.stringify({
        success: false,
        message: '指定的键不存在，无法删除',
        key: key,
        deleted: false,
        error: 'KEY_NOT_FOUND',
        timestamp: new Date().toISOString()
      }), {
        status: 404,
        headers: corsHeaders,
      });
    }

    // 调用 Cloudflare KV 存储服务删除数据
    await kvNamespace.delete(key);

    // 返回成功响应
    const response = {
      success: true,
      message: '数据删除成功',
      key: key,
      deleted: true,
      timestamp: new Date().toISOString()
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    });

  } catch (error) {
    // 错误处理
    console.error('删除数据时发生错误:', error);
    
    const errorResponse = {
      success: false,
      message: '删除数据时发生内部错误',
      error: error.message || 'INTERNAL_SERVER_ERROR',
      key: 'unknown',
      deleted: false,
      timestamp: new Date().toISOString()
    };

    // 尝试从请求中获取键名用于错误响应
    try {
      const url = new URL(request.url);
      errorResponse.key = url.searchParams.get('key') || 'unknown';
    } catch (e) {
      // 忽略 URL 解析错误
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}
